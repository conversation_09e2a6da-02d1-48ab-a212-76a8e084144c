# Generated by Django 5.1.7 on 2025-07-17 20:01

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('toy_app', '0015_cartitem_game_package'),
    ]

    operations = [
        migrations.AddField(
            model_name='gamepackage',
            name='max_selectable_games',
            field=models.PositiveIntegerField(default=2),
        ),
        migrations.AddField(
            model_name='purchase',
            name='package',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='toy_app.gamepackage'),
        ),
        migrations.AlterField(
            model_name='purchase',
            name='game',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='toy_app.game'),
        ),
        migrations.AlterField(
            model_name='purchase',
            name='purchase_type',
            field=models.CharField(choices=[('trial', 'Пробный доступ'), ('game', 'Игра'), ('package', 'Пакет')], max_length=20),
        ),
        migrations.CreateModel(
            name='PackageGameSelection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('purchase', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='toy_app.purchase')),
                ('selected_games', models.ManyToManyField(to='toy_app.game')),
            ],
        ),
    ]
