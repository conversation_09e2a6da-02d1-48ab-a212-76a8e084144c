user  nginx;
worker_processes  auto;
error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    sendfile      on;
    keepalive_timeout  65;


    # HTTP-сервер для обработки запросов
    server {
        listen 80;
        server_name delforce.kz www.delforce.kz;  #Здесь айпи адрес

        location / {
            proxy_pass http://web:8000;  # Django внутри сети Docker
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location /static/ {
            alias /static/;
        }

        location /media/ {
            alias /media/;  # Используем монтированную папку
            autoindex on;
            access_log /var/log/nginx/media_access.log;
            error_log /var/log/nginx/media_error.log;
        }
    }


    # HTTP-сервер для обслуживания ACME-челленджа и редиректа на HTTPS
#     server {
#         listen 80;
#         server_name delforce.kz www.delforce.kz;
#
#         location /.well-known/acme-challenge/ {
#             root /var/www/certbot;
#         }
#         location / {
#             return 301 https://$host$request_uri;
#         }
#     }


    # HTTPS-сервер для защищённого соединения
#     server {
#         listen 443 ssl;
#         server_name delforce.kz www.delforce.kz;
#
#         ssl_certificate /etc/letsencrypt/live/delforce.kz/fullchain.pem;
#         ssl_certificate_key /etc/letsencrypt/live/delforce.kz/privkey.pem;
#
#         location / {
#             proxy_pass http://web:8000; #ОБЯЗАТЕЛЬНОЕ НАЗВАНИЕ СЕРВИСА ИЗ КОМОПЗА
#             proxy_set_header X-Real-IP $remote_addr;
#             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#             proxy_set_header X-Forwarded-Proto $scheme;
#         }
#
#         location /static/ {
#             alias /static/;
#         }
#
#         location /media/ {
#             alias /media/;
#         }
#     }
}
