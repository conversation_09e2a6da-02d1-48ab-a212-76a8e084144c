# Generated by Django 5.1.7 on 2025-07-20 19:39

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('toy_app', '0019_remove_gamefile_file_name_remove_gamefile_file_path_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='cartitem',
            name='game_package',
        ),
        migrations.AddField(
            model_name='userlibrary',
            name='access_source',
            field=models.CharField(choices=[('individual', 'Individual Purchase'), ('package', 'Package Subscription')], default='individual', max_length=20),
        ),
        migrations.AlterField(
            model_name='usergameaccess',
            name='access_type',
            field=models.CharField(choices=[('oneday', 'One Day Access'), ('subscription', 'Subscription Access'), ('package', 'Package Access')], max_length=20),
        ),
        migrations.CreateModel(
            name='UserPackageSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('games_selected_count', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('activated_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField()),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='toy_app.gamepackage')),
                ('purchase', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='toy_app.purchase')),
                ('selected_games', models.ManyToManyField(blank=True, help_text='Выбранные игры из пакета', to='toy_app.game')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='package_subscriptions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'package')},
            },
        ),
        migrations.AddField(
            model_name='usergameaccess',
            name='package_subscription',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='toy_app.userpackagesubscription'),
        ),
        migrations.AddField(
            model_name='userlibrary',
            name='package_subscription',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='toy_app.userpackagesubscription'),
        ),
    ]
