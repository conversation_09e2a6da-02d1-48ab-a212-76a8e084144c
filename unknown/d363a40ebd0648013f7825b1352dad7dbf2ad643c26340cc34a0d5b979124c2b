# Generated by Django 5.1.7 on 2025-07-12 08:54

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('toy_app', '0005_remove_game_gallery_images_alter_game_cover_image_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserGameAccess',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('access_type', models.CharField(choices=[('oneday', 'One Day Access'), ('subscription', 'Subscription Access')], max_length=20)),
                ('access_start', models.DateTimeField()),
                ('access_end', models.DateTimeField()),
                ('game', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='toy_app.game')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
