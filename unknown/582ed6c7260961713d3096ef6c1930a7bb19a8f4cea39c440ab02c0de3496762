# Generated by Django 5.1.7 on 2025-07-17 18:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('toy_app', '0012_alter_usergameaccess_access_end_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='GamePackage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('duration_days', models.PositiveIntegerField(default=30)),
                ('price', models.DecimalField(decimal_places=2, max_digits=8)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('games', models.ManyToManyField(related_name='packages', to='toy_app.game')),
            ],
        ),
        migrations.DeleteModel(
            name='Bundle',
        ),
    ]
