# Generated by Django 5.1.7 on 2025-07-11 09:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('toy_app', '0004_alter_game_cover_image'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='game',
            name='gallery_images',
        ),
        migrations.AlterField(
            model_name='game',
            name='cover_image',
            field=models.ImageField(blank=True, null=True, upload_to='game_covers/'),
        ),
        migrations.CreateModel(
            name='GameGalleryItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.ImageField(upload_to='game_gallery/')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('game', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='gallery_items', to='toy_app.game')),
            ],
        ),
    ]
