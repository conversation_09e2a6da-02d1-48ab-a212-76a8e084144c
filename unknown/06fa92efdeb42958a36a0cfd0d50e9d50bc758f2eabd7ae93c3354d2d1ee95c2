# Generated by Django 5.1.7 on 2025-07-20 18:03

import toy_app.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('toy_app', '0018_gamefile'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='gamefile',
            name='file_name',
        ),
        migrations.RemoveField(
            model_name='gamefile',
            name='file_path',
        ),
        migrations.RemoveField(
            model_name='gamefile',
            name='file_size',
        ),
        migrations.AddField(
            model_name='gamefile',
            name='file',
            field=models.FileField(default='', help_text='Файл игры', upload_to=toy_app.models.game_file_upload_path),
            preserve_default=False,
        ),
    ]
