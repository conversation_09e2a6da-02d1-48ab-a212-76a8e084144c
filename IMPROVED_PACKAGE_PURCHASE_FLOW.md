# Improved Package Purchase Flow

## Overview
The package purchase system has been enhanced to provide a better user experience by allowing users to select games before payment, rather than after. This creates a more transparent and user-friendly purchasing process.

## Current vs Improved Flow

### ❌ Old Flow (Still Available)
1. User pays for package → `/api/packages/purchase/`
2. User selects games → `/api/packages/select-games/`
3. User gets access to selected games

**Problems:**
- User pays without knowing which games they'll choose
- Two-step process with potential for confusion
- Poor UX - payment before selection

### ✅ New Improved Flow
1. User previews package with selected games → `/api/packages/preview/`
2. User purchases package with pre-selected games → `/api/packages/purchase-with-games/`
3. User immediately gets access to selected games

**Benefits:**
- User sees exactly what they're buying before payment
- Single-step purchase process
- Better UX - selection before payment
- Immediate access after purchase

## New API Endpoints

### 1. Package Purchase Preview
**Endpoint:** `POST /api/packages/preview/`

Preview what the user will get before purchasing.

**Request:**
```json
{
    "package_id": 1,
    "selected_games": [1, 2, 3]
}
```

**Response:**
```json
{
    "preview": {
        "package": {
            "id": 1,
            "name": "Premium Package",
            "description": "Access to premium games",
            "price": 49.99,
            "duration_days": 30,
            "max_selectable_games": 3
        },
        "selected_games": [
            {
                "id": 1,
                "title": "Game 1",
                "cover_image": "game_covers/game1.jpg",
                "cover_image_url": "http://localhost:8000/media/game_covers/game1.jpg",
                "price": "19.99"
            },
            {
                "id": 2,
                "title": "Game 2",
                "cover_image": "game_covers/game2.jpg",
                "cover_image_url": "http://localhost:8000/media/game_covers/game2.jpg",
                "price": "24.99"
            }
        ],
        "total_price": 49.99,
        "games_count": 2,
        "expires_in_days": 30,
        "warnings": []
    },
    "can_purchase": true,
    "message": "Предварительный просмотр покупки пакета"
}
```

### 2. Purchase Package with Games
**Endpoint:** `POST /api/packages/purchase-with-games/`

Purchase a package with pre-selected games in one step.

**Request:**
```json
{
    "package_id": 1,
    "selected_games": [1, 2, 3]
}
```

**Response:**
```json
{
    "detail": "Пакет успешно приобретён с выбранными играми.",
    "subscription_id": 123,
    "expires_at": "2025-08-21T10:30:00Z",
    "selected_games": [
        {"id": 1, "title": "Game 1"},
        {"id": 2, "title": "Game 2"}
    ],
    "games_count": 2,
    "max_selectable_games": 3
}
```

## Validation & Error Handling

### Preview Endpoint Validations
- ✅ Package exists
- ✅ Games belong to package
- ✅ Number of games ≤ max_selectable_games
- ✅ Games exist in database
- ⚠️ Warns if user already owns some games
- ❌ Blocks if user has active subscription

### Purchase Endpoint Validations
- ✅ All preview validations
- ❌ Blocks if user already owns any selected games
- ❌ Blocks if user has active subscription
- ❌ Blocks if no games selected

### Error Response Examples

**Too many games:**
```json
{
    "error": "Вы можете выбрать максимум 2 игр."
}
```

**Games not in package:**
```json
{
    "error": "Игры с ID [5, 6] не входят в этот пакет."
}
```

**Already owned games:**
```json
{
    "error": "У вас уже есть активный доступ к некоторым играм.",
    "already_owned_games": [
        {"id": 1, "title": "Game 1"}
    ]
}
```

## Frontend Implementation Example

### JavaScript/React Example
```javascript
// Step 1: Preview the purchase
const previewPurchase = async (packageId, selectedGames) => {
    const response = await fetch('/api/packages/preview/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
            package_id: packageId,
            selected_games: selectedGames
        })
    });
    
    const data = await response.json();
    
    if (data.can_purchase) {
        // Show preview to user
        showPurchasePreview(data.preview);
    } else {
        // Show warnings
        showWarnings(data.preview.warnings);
    }
};

// Step 2: Complete the purchase
const completePurchase = async (packageId, selectedGames) => {
    const response = await fetch('/api/packages/purchase-with-games/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
            package_id: packageId,
            selected_games: selectedGames
        })
    });
    
    if (response.ok) {
        const data = await response.json();
        // Redirect to library or show success
        showPurchaseSuccess(data);
    } else {
        const error = await response.json();
        showError(error.error);
    }
};
```

## Migration Strategy

The old endpoints remain available for backward compatibility:
- `/api/packages/purchase/` - Original purchase endpoint
- `/api/packages/select-games/` - Original game selection endpoint

New applications should use the improved flow:
- `/api/packages/preview/` - Preview purchase
- `/api/packages/purchase-with-games/` - Complete purchase

## Benefits Summary

1. **Better UX**: Users see exactly what they're buying
2. **Reduced Confusion**: Single-step purchase process
3. **Transparency**: Clear preview before payment
4. **Error Prevention**: Validation before payment
5. **Immediate Access**: Games available right after purchase
6. **Backward Compatibility**: Old endpoints still work
