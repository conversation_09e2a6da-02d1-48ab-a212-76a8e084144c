# Generated by Django 5.1.7 on 2025-07-20 17:48

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('toy_app', '0017_alter_cartitem_game'),
    ]

    operations = [
        migrations.CreateModel(
            name='GameFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_name', models.CharField(help_text='Название файла', max_length=255)),
                ('file_path', models.Char<PERSON>ield(help_text='Путь к файлу или URL', max_length=500)),
                ('platform', models.CharField(choices=[('windows', 'Windows'), ('mac', 'macOS'), ('linux', 'Linux'), ('android', 'Android'), ('ios', 'iOS'), ('web', 'Web')], help_text='Платформа', max_length=20)),
                ('version', models.CharField(blank=True, help_text='Версия игры', max_length=50, null=True)),
                ('file_size', models.BigIntegerField(blank=True, help_text='Размер файла в байтах', null=True)),
                ('description', models.TextField(blank=True, help_text='Описание файла', null=True)),
                ('is_active', models.BooleanField(default=True, help_text='Активен ли файл')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('game', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='game_files', to='toy_app.game')),
            ],
            options={
                'unique_together': {('game', 'platform', 'version')},
            },
        ),
    ]
