# toy_app/permissions.py

from rest_framework.permissions import BasePermission, SAFE_METHODS

class IsStaffOrReadOnly(BasePermission):
    """
    Разрешает только staff-пользователям изменять данные.
    Всем остальным разрешено только чтение (GET, HEAD, OPTIONS).
    """

    def has_permission(self, request, view):
        if request.method in SAFE_METHODS:
            return True  # GET, HEAD, OPTIONS доступны всем
        return request.user and request.user.is_staff  # Только staff может POST, PUT, DELETE


class IsAdminOrOwner(BasePermission):
    """
        Разрешает только staff-пользователям и владельцам изменять данные.
    """


    def has_permission(self, request, view):
        return request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        return request.user.is_staff or obj.assigned_to_user == request.user