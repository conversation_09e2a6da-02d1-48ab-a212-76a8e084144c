from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>

from .views import *
from rest_framework_simplejwt.views import (
    TokenRefreshView,
    TokenVerifyView,
)

from .views import CustomTokenObtainPairView


# Создаем router для игр
router = DefaultRouter()
router.register(r'games', GameViewSet, basename='games')
router.register(r'cart', CartItemViewSet, basename='cart')
router.register(r'game-keys', GameKeyViewSet, basename='game-keys')
router.register(r'purchases', PurchaseViewSet, basename='purchases')
router.register(r'gallery', GameGalleryItemViewSet, basename='gallery')
router.register(r'verification-codes', EmailVerificationCodeViewSet, basename='verification-code')
router.register(r'admin/users', AdminUserViewSet, basename='admin-users')
router.register(r'admin/game-access', AdminUserGameAccessViewSet, basename='admin-game-access')
router.register(r'game-packages', GamePackageViewSet, basename='game-package')
router.register(r'game-files', GameFileViewSet, basename='game-files')



urlpatterns = [

    # register user
    path('api/auth/register/', RegisterView.as_view(), name='register'),
    path('api/auth/verify-code/', VerifyCodeView.as_view(), name='verify-code'),
    path('api/auth/resend-code/', ResendCodeView.as_view(), name='resend-code'),
    path('api/userprofile/', UserProfile.as_view(), name='me'),
    path('api/users/', UserList.as_view(), name='user-list'),
    path('api/user/change-password/', ChangePasswordView.as_view(), name='change-password'),




    # tokens
    path('api/token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('api/token/verify/', TokenVerifyView.as_view(), name='token_verify'),


    # game library
    path('api/library/', UserLibraryListView.as_view(), name='user-library'),
    path('api/library/add/', AddToLibraryView.as_view(), name='add-to-library'),

    #checkout
    path('api/checkout/', CheckoutView.as_view(), name='checkout'),


    # Fake buy items
    path('api/purchases/<int:pk>/pay/', PurchasePaymentView.as_view(), name='purchase-pay'),

    # Активация для одного дня.
    path('api/access/activate/', ActivateAccessView.as_view(), name='access-activate'),


    #statistics
    path('api/user/summary/', UserSummaryView.as_view(), name='user-summary'),
    # gallery/upload
    path('api/gallery/upload/', UploadGalleryImageView.as_view(), name='upload-gallery-image'),

    # game files
    path('api/game-files/<int:file_id>/download/', DownloadGameFileView.as_view(), name='download-game-file'),

    # packages
    path('api/packages/purchase/', PurchasePackageView.as_view(), name='purchase-package'),
    path('api/packages/purchase-with-games/', PurchasePackageWithGamesView.as_view(), name='purchase-package-with-games'),
    path('api/packages/preview/', PackagePurchasePreviewView.as_view(), name='package-purchase-preview'),
    path('api/packages/select-games/', SelectPackageGamesView.as_view(), name='select-package-games'),
    path('api/my-packages/', UserPackageSubscriptionsView.as_view(), name='my-packages'),
    path('api/my-packages/<int:pk>/', PackageSubscriptionDetailView.as_view(), name='package-detail'),

    # access to games
    path('api/my-games/', UserGameAccessListView.as_view(), name='my-games'),
    # access to unity game
    path('api/auth/game-access/', GameAccessAuthView.as_view(), name='game-access-auth'),


    path('api/', include(router.urls)),




]