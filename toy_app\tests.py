from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from rest_framework.test import APITestCase
from rest_framework import status
from django.urls import reverse

from .models import GamePackage, UserPackageSubscription, Purchase, Game, CartItem, UserGameAccess, UserLibrary
from .serializers import GamePackageSerializer

User = get_user_model()


class GamePackageSubscriptionTestCase(TestCase):
    """Test cases for the has_active_subscription functionality"""

    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()

        # Create test users
        self.user_with_subscription = User.objects.create(
            email='<EMAIL>',
            user_code='SUB01'
        )

        self.user_without_subscription = User.objects.create(
            email='<EMAIL>',
            user_code='NOSUB'
        )

        # Create test package
        self.package = GamePackage.objects.create(
            name='Test Package',
            description='Test package for subscription testing',
            price=29.99,
            duration_days=30,
            max_selectable_games=2
        )

        # Create active subscription for first user
        self.purchase = Purchase.objects.create(
            user=self.user_with_subscription,
            package=self.package,
            purchase_type='package',
            price=self.package.price,
            status='paid'
        )

        self.subscription = UserPackageSubscription.objects.create(
            user=self.user_with_subscription,
            package=self.package,
            purchase=self.purchase,
            expires_at=timezone.now() + timedelta(days=15),
            is_active=True
        )

    def test_user_with_active_subscription(self):
        """Test that user with active subscription sees has_active_subscription=True"""
        request = self.factory.get('/api/game-packages/')
        request.user = self.user_with_subscription

        serializer = GamePackageSerializer(self.package, context={'request': request})
        data = serializer.data

        self.assertTrue(data['has_active_subscription'])

    def test_user_without_subscription(self):
        """Test that user without subscription sees has_active_subscription=False"""
        request = self.factory.get('/api/game-packages/')
        request.user = self.user_without_subscription

        serializer = GamePackageSerializer(self.package, context={'request': request})
        data = serializer.data

        self.assertFalse(data['has_active_subscription'])

    def test_user_with_expired_subscription(self):
        """Test that user with expired subscription sees has_active_subscription=False"""
        # Make subscription expired
        self.subscription.expires_at = timezone.now() - timedelta(days=1)
        self.subscription.save()

        request = self.factory.get('/api/game-packages/')
        request.user = self.user_with_subscription

        serializer = GamePackageSerializer(self.package, context={'request': request})
        data = serializer.data

        self.assertFalse(data['has_active_subscription'])

    def test_unauthenticated_user(self):
        """Test that unauthenticated user sees has_active_subscription=False"""
        request = self.factory.get('/api/game-packages/')
        request.user = None

        serializer = GamePackageSerializer(self.package, context={'request': request})
        data = serializer.data

        self.assertFalse(data['has_active_subscription'])

    def test_inactive_subscription(self):
        """Test that user with inactive subscription sees has_active_subscription=False"""
        # Make subscription inactive
        self.subscription.is_active = False
        self.subscription.save()

        request = self.factory.get('/api/game-packages/')
        request.user = self.user_with_subscription

        serializer = GamePackageSerializer(self.package, context={'request': request})
        data = serializer.data

        self.assertFalse(data['has_active_subscription'])


class MyPackagesImageTestCase(TestCase):
    """Test cases for the available_games field including cover_image"""

    def setUp(self):
        """Set up test data"""
        # Create test user
        self.user = User.objects.create(
            email='<EMAIL>',
            user_code='IMG01'
        )

        # Create test games with and without images
        self.game_with_image = Game.objects.create(
            title='Game With Image',
            game_code='GWI01',
            description='Game that has a cover image',
            price=19.99,
            cover_image='game_covers/test_image.jpg'  # Simulated image path
        )

        self.game_without_image = Game.objects.create(
            title='Game Without Image',
            game_code='GWO01',
            description='Game that has no cover image',
            price=24.99
            # cover_image is None/blank
        )

        # Create test package
        self.package = GamePackage.objects.create(
            name='Image Test Package',
            description='Package for testing image inclusion',
            price=39.99,
            duration_days=30,
            max_selectable_games=2
        )

        # Add games to package
        self.package.games.add(self.game_with_image, self.game_without_image)

        # Create subscription
        self.purchase = Purchase.objects.create(
            user=self.user,
            package=self.package,
            purchase_type='package',
            price=self.package.price,
            status='paid'
        )

        self.subscription = UserPackageSubscription.objects.create(
            user=self.user,
            package=self.package,
            purchase=self.purchase,
            expires_at=timezone.now() + timedelta(days=15),
            is_active=True
        )

    def test_available_games_includes_cover_image(self):
        """Test that available_games field includes cover_image"""
        from .serializers import UserPackageSubscriptionSerializer

        serializer = UserPackageSubscriptionSerializer(self.subscription)
        data = serializer.data

        # Check that available_games is present
        self.assertIn('available_games', data)

        # Check that we have games available
        available_games = data['available_games']
        self.assertEqual(len(available_games), 2)  # Both games should be available

        # Check that each game has the required fields including cover_image
        for game in available_games:
            self.assertIn('id', game)
            self.assertIn('title', game)
            self.assertIn('cover_image', game)  # This is the key test

        # Find specific games and check their cover_image values
        game_with_image_data = next(g for g in available_games if g['title'] == 'Game With Image')
        game_without_image_data = next(g for g in available_games if g['title'] == 'Game Without Image')

        # Game with image should have cover_image value
        self.assertIsNotNone(game_with_image_data['cover_image'])
        self.assertIn('test_image.jpg', game_with_image_data['cover_image'])

        # Game without image should have null/empty cover_image
        self.assertIsNone(game_without_image_data['cover_image'])

    def test_selected_games_not_in_available_games(self):
        """Test that selected games are excluded from available_games"""
        from .serializers import UserPackageSubscriptionSerializer

        # Select one game
        self.subscription.selected_games.add(self.game_with_image)
        self.subscription.games_selected_count = 1
        self.subscription.save()

        serializer = UserPackageSubscriptionSerializer(self.subscription)
        data = serializer.data

        # Should only have one available game now
        available_games = data['available_games']
        self.assertEqual(len(available_games), 1)

        # The available game should be the one we didn't select
        self.assertEqual(available_games[0]['title'], 'Game Without Image')
        self.assertIn('cover_image', available_games[0])


class UserProfileSubscriptionTestCase(TestCase):
    """Test cases for active subscriptions in user profile endpoint"""

    def setUp(self):
        """Set up test data"""
        # Create test user
        self.user = User.objects.create(
            email='<EMAIL>',
            user_code='PROF1'
        )

        # Create test package
        self.package = GamePackage.objects.create(
            name='Profile Test Package',
            description='Package for testing profile endpoint',
            price=29.99,
            duration_days=30,
            max_selectable_games=2
        )

        # Create test game
        self.game = Game.objects.create(
            title='Profile Test Game',
            game_code='PTG01',
            description='Game for profile testing',
            price=19.99
        )

        self.package.games.add(self.game)

    def test_userprofile_without_subscription(self):
        """Test user profile without any subscriptions"""
        from .serializers import UserSelfUpdateSerializer

        serializer = UserSelfUpdateSerializer(self.user)
        data = serializer.data

        # Check that active_subscriptions field is present
        self.assertIn('active_subscriptions', data)

        # Should be empty list when no subscriptions
        self.assertEqual(data['active_subscriptions'], [])

    def test_userprofile_with_active_subscription(self):
        """Test user profile with active subscription"""
        from .serializers import UserSelfUpdateSerializer

        # Create active subscription
        purchase = Purchase.objects.create(
            user=self.user,
            package=self.package,
            purchase_type='package',
            price=self.package.price,
            status='paid'
        )

        subscription = UserPackageSubscription.objects.create(
            user=self.user,
            package=self.package,
            purchase=purchase,
            expires_at=timezone.now() + timedelta(days=15),
            is_active=True
        )

        serializer = UserSelfUpdateSerializer(self.user)
        data = serializer.data

        # Check that active_subscriptions field is present and has data
        self.assertIn('active_subscriptions', data)
        self.assertEqual(len(data['active_subscriptions']), 1)

        # Check subscription data structure
        subscription_data = data['active_subscriptions'][0]
        self.assertEqual(subscription_data['id'], subscription.id)
        self.assertEqual(subscription_data['package_name'], self.package.name)
        self.assertEqual(subscription_data['package_description'], self.package.description)
        self.assertIn('expires_at', subscription_data)
        self.assertIn('remaining_slots', subscription_data)
        self.assertIn('available_games', subscription_data)
        self.assertIn('selected_games', subscription_data)

    def test_userprofile_with_expired_subscription(self):
        """Test user profile with expired subscription (should not appear)"""
        from .serializers import UserSelfUpdateSerializer

        # Create expired subscription
        purchase = Purchase.objects.create(
            user=self.user,
            package=self.package,
            purchase_type='package',
            price=self.package.price,
            status='paid'
        )

        subscription = UserPackageSubscription.objects.create(
            user=self.user,
            package=self.package,
            purchase=purchase,
            expires_at=timezone.now() - timedelta(days=1),  # Expired
            is_active=True
        )

        serializer = UserSelfUpdateSerializer(self.user)
        data = serializer.data

        # Expired subscription should not appear in active_subscriptions
        self.assertIn('active_subscriptions', data)
        self.assertEqual(data['active_subscriptions'], [])


class SingleSubscriptionRestrictionTestCase(TestCase):
    """Test cases for single subscription restriction"""

    def setUp(self):
        """Set up test data"""
        # Create test user
        self.user = User.objects.create(
            email='<EMAIL>',
            user_code='SING1'
        )

        # Create two test packages
        self.package1 = GamePackage.objects.create(
            name='Package 1',
            description='First test package',
            price=29.99,
            duration_days=30,
            max_selectable_games=2
        )

        self.package2 = GamePackage.objects.create(
            name='Package 2',
            description='Second test package',
            price=39.99,
            duration_days=30,
            max_selectable_games=3
        )

        self.factory = RequestFactory()

    def test_user_can_create_first_subscription(self):
        """Test that user can create their first subscription"""
        # Verify no existing subscriptions
        existing_subscriptions = UserPackageSubscription.objects.filter(
            user=self.user,
            is_active=True,
            expires_at__gt=timezone.now()
        )
        self.assertEqual(existing_subscriptions.count(), 0)

        # Create first subscription
        purchase = Purchase.objects.create(
            user=self.user,
            package=self.package1,
            purchase_type='package',
            price=self.package1.price,
            status='paid'
        )

        subscription = UserPackageSubscription.objects.create(
            user=self.user,
            package=self.package1,
            purchase=purchase,
            expires_at=timezone.now() + timedelta(days=15),
            is_active=True
        )

        # Verify subscription was created
        self.assertIsNotNone(subscription.id)
        self.assertEqual(subscription.package, self.package1)

    def test_user_cannot_have_multiple_active_subscriptions(self):
        """Test that user cannot have multiple active subscriptions"""
        # Create first active subscription
        purchase1 = Purchase.objects.create(
            user=self.user,
            package=self.package1,
            purchase_type='package',
            price=self.package1.price,
            status='paid'
        )

        subscription1 = UserPackageSubscription.objects.create(
            user=self.user,
            package=self.package1,
            purchase=purchase1,
            expires_at=timezone.now() + timedelta(days=15),
            is_active=True
        )

        # Verify that checking for existing subscription works
        existing_subscription = UserPackageSubscription.objects.filter(
            user=self.user,
            is_active=True,
            expires_at__gt=timezone.now()
        ).first()

        self.assertIsNotNone(existing_subscription)
        self.assertEqual(existing_subscription.package, self.package1)

    def test_game_packages_api_shows_subscription_status(self):
        """Test that game packages API shows both subscription status fields"""
        # Test without subscription
        request = self.factory.get('/api/game-packages/')
        request.user = self.user

        serializer = GamePackageSerializer(self.package1, context={'request': request})
        data = serializer.data

        self.assertFalse(data['has_active_subscription'])
        self.assertFalse(data['user_has_any_active_subscription'])

        # Create subscription to package1
        purchase = Purchase.objects.create(
            user=self.user,
            package=self.package1,
            purchase_type='package',
            price=self.package1.price,
            status='paid'
        )

        subscription = UserPackageSubscription.objects.create(
            user=self.user,
            package=self.package1,
            purchase=purchase,
            expires_at=timezone.now() + timedelta(days=15),
            is_active=True
        )

        # Test package1 (user has subscription to this package)
        serializer1 = GamePackageSerializer(self.package1, context={'request': request})
        data1 = serializer1.data

        self.assertTrue(data1['has_active_subscription'])  # Has subscription to THIS package
        self.assertTrue(data1['user_has_any_active_subscription'])  # Has subscription to ANY package

        # Test package2 (user doesn't have subscription to this package, but has subscription to another)
        serializer2 = GamePackageSerializer(self.package2, context={'request': request})
        data2 = serializer2.data

        self.assertFalse(data2['has_active_subscription'])  # No subscription to THIS package
        self.assertTrue(data2['user_has_any_active_subscription'])  # But has subscription to ANOTHER package


class BulkCartTestCase(APITestCase):
    """Test cases for bulk cart functionality"""

    def setUp(self):
        """Set up test data"""
        # Create test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            user_code='CART1',
            password='testpass123'
        )

        # Create test games
        self.game1 = Game.objects.create(
            title='Game 1',
            game_code='G001',
            description='First test game',
            price=19.99
        )

        self.game2 = Game.objects.create(
            title='Game 2',
            game_code='G002',
            description='Second test game',
            price=24.99
        )

        self.game3 = Game.objects.create(
            title='Game 3',
            game_code='G003',
            description='Third test game',
            price=29.99
        )

        # Authenticate user for API calls
        self.client.force_authenticate(user=self.user)

    def test_add_multiple_games_with_array(self):
        """Test adding multiple games using array format"""
        url = '/api/cart/add-multiple/'
        data = {
            'games': [self.game1.id, self.game2.id, self.game3.id]
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['summary']['added'], 3)
        self.assertEqual(response.data['summary']['skipped'], 0)
        self.assertEqual(response.data['summary']['errors'], 0)

        # Verify games were added to cart
        cart_items = CartItem.objects.filter(user=self.user)
        self.assertEqual(cart_items.count(), 3)

    def test_add_multiple_games_with_csv(self):
        """Test adding multiple games using CSV format"""
        url = '/api/cart/add-multiple/'
        data = {
            'games_csv': f'{self.game1.id},{self.game2.id},{self.game3.id}'
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['summary']['added'], 3)

        # Verify games were added to cart
        cart_items = CartItem.objects.filter(user=self.user)
        self.assertEqual(cart_items.count(), 3)

    def test_add_games_with_duplicates_in_request(self):
        """Test adding games with duplicates in the same request"""
        url = '/api/cart/add-multiple/'
        data = {
            'games': [self.game1.id, self.game2.id, self.game1.id]  # game1 appears twice
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['summary']['added'], 2)  # Only 2 unique games added

        # Verify only unique games were added
        cart_items = CartItem.objects.filter(user=self.user)
        self.assertEqual(cart_items.count(), 2)

    def test_add_games_already_in_cart(self):
        """Test adding games that are already in cart"""
        # Add game1 to cart first
        CartItem.objects.create(user=self.user, game=self.game1)

        url = '/api/cart/add-multiple/'
        data = {
            'games': [self.game1.id, self.game2.id]
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['summary']['added'], 1)  # Only game2 added
        self.assertEqual(response.data['summary']['skipped'], 1)  # game1 skipped

        # Check skipped reason
        skipped_item = response.data['results']['skipped'][0]
        self.assertEqual(skipped_item['game_id'], self.game1.id)
        self.assertEqual(skipped_item['reason'], 'Already in cart')

    def test_add_games_with_active_access(self):
        """Test adding games user already has active access to"""
        # Create active access for game1
        UserGameAccess.objects.create(
            user=self.user,
            game=self.game1,
            access_start=timezone.now() - timedelta(hours=1),
            access_end=timezone.now() + timedelta(days=1),
            activated=True
        )

        url = '/api/cart/add-multiple/'
        data = {
            'games': [self.game1.id, self.game2.id]
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['summary']['added'], 1)  # Only game2 added
        self.assertEqual(response.data['summary']['skipped'], 1)  # game1 skipped

        # Check skipped reason
        skipped_item = response.data['results']['skipped'][0]
        self.assertEqual(skipped_item['game_id'], self.game1.id)
        self.assertEqual(skipped_item['reason'], 'Already has active access')

    def test_add_nonexistent_games(self):
        """Test adding games that don't exist"""
        url = '/api/cart/add-multiple/'
        data = {
            'games': [999, 1000]  # Non-existent game IDs
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Games not found', response.data['error'])

    def test_add_games_empty_request(self):
        """Test adding games with empty request"""
        url = '/api/cart/add-multiple/'
        data = {}

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('No games provided', response.data['error'])

    def test_add_games_mixed_array_and_csv(self):
        """Test adding games using both array and CSV in same request"""
        url = '/api/cart/add-multiple/'
        data = {
            'games': [self.game1.id],
            'games_csv': f'{self.game2.id},{self.game3.id}'
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['summary']['added'], 3)  # All 3 games added

        # Verify all games were added to cart
        cart_items = CartItem.objects.filter(user=self.user)
        self.assertEqual(cart_items.count(), 3)

    def test_unauthenticated_user_cannot_add_to_cart(self):
        """Test that unauthenticated users cannot add to cart"""
        self.client.force_authenticate(user=None)  # Remove authentication

        url = '/api/cart/add-multiple/'
        data = {
            'games': [self.game1.id]
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class ImprovedPackagePurchaseTestCase(APITestCase):
    """Test cases for improved package purchase flow with game selection"""

    def setUp(self):
        """Set up test data"""
        # Create test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            user_code='PKG01',
            password='testpass123'
        )

        # Create test games
        self.game1 = Game.objects.create(
            title='Package Game 1',
            game_code='PG001',
            description='First package game',
            price=19.99
        )

        self.game2 = Game.objects.create(
            title='Package Game 2',
            game_code='PG002',
            description='Second package game',
            price=24.99
        )

        self.game3 = Game.objects.create(
            title='Package Game 3',
            game_code='PG003',
            description='Third package game',
            price=29.99
        )

        self.game4 = Game.objects.create(
            title='Package Game 4',
            game_code='PG004',
            description='Fourth package game',
            price=34.99
        )

        # Create test package
        self.package = GamePackage.objects.create(
            name='Test Package',
            description='Test package for improved purchase flow',
            price=49.99,
            duration_days=30,
            max_selectable_games=2
        )

        # Add games to package
        self.package.games.add(self.game1, self.game2, self.game3, self.game4)

        # Authenticate user for API calls
        self.client.force_authenticate(user=self.user)

    def test_package_purchase_preview_success(self):
        """Test successful package purchase preview"""
        url = '/api/packages/preview/'
        data = {
            'package_id': self.package.id,
            'selected_games': [self.game1.id, self.game2.id]
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['can_purchase'])
        self.assertIn('preview', response.data)

        preview = response.data['preview']
        self.assertEqual(preview['package']['id'], self.package.id)
        self.assertEqual(preview['games_count'], 2)
        self.assertEqual(len(preview['selected_games']), 2)

    def test_package_purchase_with_games_success(self):
        """Test successful package purchase with game selection"""
        url = '/api/packages/purchase-with-games/'
        data = {
            'package_id': self.package.id,
            'selected_games': [self.game1.id, self.game2.id]
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('subscription_id', response.data)
        self.assertEqual(response.data['games_count'], 2)

        # Verify subscription was created
        subscription = UserPackageSubscription.objects.get(
            id=response.data['subscription_id']
        )
        self.assertEqual(subscription.user, self.user)
        self.assertEqual(subscription.package, self.package)
        self.assertEqual(subscription.games_selected_count, 2)

        # Verify games were selected
        selected_games = subscription.selected_games.all()
        self.assertEqual(selected_games.count(), 2)
        self.assertIn(self.game1, selected_games)
        self.assertIn(self.game2, selected_games)

        # Verify access was granted
        for game in [self.game1, self.game2]:
            access = UserGameAccess.objects.filter(
                user=self.user,
                game=game,
                access_type='package',
                activated=True
            ).first()
            self.assertIsNotNone(access)

        # Verify games were added to library
        for game in [self.game1, self.game2]:
            library_entry = UserLibrary.objects.filter(
                user=self.user,
                game=game,
                access_source='package'
            ).first()
            self.assertIsNotNone(library_entry)

    def test_package_purchase_too_many_games(self):
        """Test package purchase with too many games selected"""
        url = '/api/packages/purchase-with-games/'
        data = {
            'package_id': self.package.id,
            'selected_games': [self.game1.id, self.game2.id, self.game3.id]  # 3 games, max is 2
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('максимум 2 игр', response.data['error'])

    def test_package_purchase_invalid_games(self):
        """Test package purchase with games not in package"""
        # Create a game not in the package
        other_game = Game.objects.create(
            title='Other Game',
            game_code='OG001',
            description='Game not in package',
            price=39.99
        )

        url = '/api/packages/purchase-with-games/'
        data = {
            'package_id': self.package.id,
            'selected_games': [self.game1.id, other_game.id]
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('не входят в этот пакет', response.data['error'])

    def test_package_purchase_already_owned_games(self):
        """Test package purchase with games user already owns"""
        # Create active access for game1
        UserGameAccess.objects.create(
            user=self.user,
            game=self.game1,
            access_type='individual',
            access_start=timezone.now() - timedelta(hours=1),
            access_end=timezone.now() + timedelta(days=1),
            activated=True
        )

        url = '/api/packages/purchase-with-games/'
        data = {
            'package_id': self.package.id,
            'selected_games': [self.game1.id, self.game2.id]
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('уже есть активный доступ', response.data['error'])
        self.assertIn('already_owned_games', response.data)

    def test_package_purchase_with_existing_subscription(self):
        """Test package purchase when user already has active subscription"""
        # Create existing subscription
        other_package = GamePackage.objects.create(
            name='Other Package',
            description='Another package',
            price=39.99,
            duration_days=30,
            max_selectable_games=1
        )

        existing_purchase = Purchase.objects.create(
            user=self.user,
            package=other_package,
            purchase_type='package',
            price=other_package.price,
            status='paid'
        )

        UserPackageSubscription.objects.create(
            user=self.user,
            package=other_package,
            purchase=existing_purchase,
            expires_at=timezone.now() + timedelta(days=15),
            is_active=True
        )

        url = '/api/packages/purchase-with-games/'
        data = {
            'package_id': self.package.id,
            'selected_games': [self.game1.id, self.game2.id]
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('уже есть активная подписка', response.data['error'])

    def test_package_preview_with_owned_games_warning(self):
        """Test package preview shows warning for already owned games"""
        # Create active access for game1
        UserGameAccess.objects.create(
            user=self.user,
            game=self.game1,
            access_type='individual',
            access_start=timezone.now() - timedelta(hours=1),
            access_end=timezone.now() + timedelta(days=1),
            activated=True
        )

        url = '/api/packages/preview/'
        data = {
            'package_id': self.package.id,
            'selected_games': [self.game1.id, self.game2.id]
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertFalse(response.data['can_purchase'])
        self.assertIn('warnings', response.data['preview'])

        warnings = response.data['preview']['warnings']
        self.assertEqual(len(warnings), 1)
        self.assertEqual(warnings[0]['type'], 'already_owned_games')

    def test_package_purchase_empty_games_list(self):
        """Test package purchase with empty games list"""
        url = '/api/packages/purchase-with-games/'
        data = {
            'package_id': self.package.id,
            'selected_games': []
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('selected_games обязательны', response.data['error'])


class GamePackageImageTestCase(APITestCase):
    """Test cases for game package endpoint with cover images"""

    def setUp(self):
        """Set up test data"""
        # Create test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            user_code='PKG02',
            password='testpass123'
        )

        # Create test games with and without images
        self.game_with_image = Game.objects.create(
            title='Game With Image',
            game_code='GWI01',
            description='Game that has a cover image',
            price=19.99,
            cover_image='game_covers/test_image.jpg'  # Simulated image path
        )

        self.game_without_image = Game.objects.create(
            title='Game Without Image',
            game_code='GWO01',
            description='Game that has no cover image',
            price=24.99
            # cover_image is None/blank
        )

        # Create test package
        self.package = GamePackage.objects.create(
            name='Image Test Package',
            description='Package for testing image inclusion',
            price=39.99,
            duration_days=30,
            max_selectable_games=2
        )

        # Add games to package
        self.package.games.add(self.game_with_image, self.game_without_image)

        # Authenticate user for API calls
        self.client.force_authenticate(user=self.user)

    def test_game_package_includes_cover_images(self):
        """Test that game package endpoint includes cover images for games"""
        url = f'/api/game-packages/{self.package.id}/'

        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that games are present
        self.assertIn('games', response.data)
        games = response.data['games']
        self.assertEqual(len(games), 2)

        # Check that each game has the required fields including cover_image
        for game in games:
            self.assertIn('id', game)
            self.assertIn('title', game)
            self.assertIn('cover_image', game)
            self.assertIn('cover_image_url', game)
            self.assertIn('price', game)

        # Find specific games and check their cover_image values
        game_with_image_data = next(g for g in games if g['title'] == 'Game With Image')
        game_without_image_data = next(g for g in games if g['title'] == 'Game Without Image')

        # Game with image should have cover_image value
        self.assertIsNotNone(game_with_image_data['cover_image'])
        self.assertIn('test_image.jpg', game_with_image_data['cover_image'])
        self.assertIsNotNone(game_with_image_data['cover_image_url'])

        # Game without image should have null/empty cover_image
        self.assertIsNone(game_without_image_data['cover_image'])
        self.assertIsNone(game_without_image_data['cover_image_url'])

    def test_game_package_list_includes_cover_images(self):
        """Test that game package list endpoint includes cover images"""
        url = '/api/game-packages/'

        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check if response is paginated
        if 'results' in response.data:
            packages = response.data['results']
        else:
            packages = response.data

        # Find our test package
        test_package = None
        for package in packages:
            if package['id'] == self.package.id:
                test_package = package
                break

        self.assertIsNotNone(test_package)

        # Check that games include cover images
        games = test_package['games']
        self.assertEqual(len(games), 2)

        for game in games:
            self.assertIn('cover_image', game)
            self.assertIn('cover_image_url', game)
            self.assertIn('price', game)
