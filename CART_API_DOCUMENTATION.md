# Cart API - Enhanced with Access Duration Support

## Overview
The cart API now supports:
1. Adding multiple products at once through a bulk endpoint
2. **NEW**: Choosing access duration (day/month) when adding games to cart
3. **NEW**: Updating access duration for existing cart items
4. **NEW**: Cart preview with pricing breakdown

## New Features

### Access Duration Support
- **Day Access**: 1-day access at base game price
- **Month Access**: 30-day access at 2.5x base game price
- Immediate activation for both types

## Endpoints

### 1. Bulk Add Multiple Products
```
POST /api/cart/add-multiple/
```

### 2. Update Access Duration
```
PATCH /api/cart/{cart_item_id}/update-access-duration/
```

### 3. Cart Preview
```
GET /api/cart/preview/
```

## Authentication
Requires authentication (Bearer token or session authentication).

## Request Formats

### Bulk Add - Option 1: Array Format with Access Duration
```json
{
    "games": [1, 2, 3, 4, 5],
    "access_duration": "month"
}
```

### Bulk Add - Option 2: Comma-Separated String with Access Duration
```json
{
    "games_csv": "1,2,3,4,5",
    "access_duration": "day"
}
```

### Bulk Add - Option 3: Mixed Format (both will be processed)
```json
{
    "games": [1, 2],
    "games_csv": "3,4,5",
    "access_duration": "month"
}
```

## Response Format

### Success Response (201 Created)
```json
{
    "message": "Successfully added 3 games to cart",
    "results": {
        "added": [
            {
                "game_id": 1,
                "game_title": "Game Title 1",
                "cart_item_id": 123
            },
            {
                "game_id": 2,
                "game_title": "Game Title 2", 
                "cart_item_id": 124
            }
        ],
        "skipped": [
            {
                "game_id": 3,
                "game_title": "Game Title 3",
                "reason": "Already in cart"
            }
        ],
        "errors": []
    },
    "summary": {
        "total_requested": 3,
        "added": 2,
        "skipped": 1,
        "errors": 0
    }
}
```

### Error Response (400 Bad Request)
```json
{
    "error": "Games not found: [999, 1000]"
}
```

## Validation Rules

1. **Duplicate Prevention**: Games already in the user's cart will be skipped
2. **Access Check**: Games the user already has active access to will be skipped
3. **Existence Check**: Non-existent game IDs will cause an error
4. **Duplicate Removal**: Duplicate IDs in the same request are automatically removed
5. **Authentication**: Unauthenticated users will receive a 401 error

## Example Usage

### JavaScript/Fetch
```javascript
// Add multiple games using array
const response = await fetch('/api/cart/add-multiple/', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer your-token-here'
    },
    body: JSON.stringify({
        games: [1, 2, 3, 4, 5]
    })
});

const result = await response.json();
console.log(`Added ${result.summary.added} games to cart`);
```

### cURL
```bash
# Using array format
curl -X POST http://localhost:8000/api/cart/add-multiple/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token-here" \
  -d '{"games": [1, 2, 3, 4, 5]}'

# Using CSV format  
curl -X POST http://localhost:8000/api/cart/add-multiple/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token-here" \
  -d '{"games_csv": "1,2,3,4,5"}'
```

### Python/Requests
```python
import requests

url = "http://localhost:8000/api/cart/add-multiple/"
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer your-token-here"
}

# Using array format
data = {"games": [1, 2, 3, 4, 5]}
response = requests.post(url, json=data, headers=headers)

if response.status_code == 201:
    result = response.json()
    print(f"Successfully added {result['summary']['added']} games")
else:
    print(f"Error: {response.json()}")
```

## Status Codes

- **201 Created**: Games successfully added to cart
- **200 OK**: Request processed but all games were skipped (already in cart/have access)
- **400 Bad Request**: Invalid request (no games provided, games not found, etc.)
- **401 Unauthorized**: Authentication required

## Notes

- The endpoint processes games individually, so partial success is possible
- Games are added with quantity=1 (same as single add endpoint)
- The response includes detailed information about what was added, skipped, or failed
- Duplicate game IDs within the same request are automatically deduplicated
- Both `games` array and `games_csv` string can be used in the same request
