from datetime import timed<PERSON><PERSON>

from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin, BaseUserManager
from django.db import models
from django.utils import timezone
from rest_framework.exceptions import ValidationError

from DjangoBaseApp import settings
import random
import string

# Create your models here.


# --- Пользователь ---
class UserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError('Email должен быть указан')

        if 'user_code' not in extra_fields or not extra_fields['user_code']:
            extra_fields['user_code'] = self.generate_unique_code()

        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save()
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        return self.create_user(email, password, **extra_fields)

    def generate_unique_code(self, length=5):
        while True:
            code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))
            if not User.objects.filter(user_code=code).exists():
                return code




class User(AbstractBaseUser, PermissionsMixin):
    email = models.EmailField(unique=True)
    phone = models.CharField(max_length=11, unique=True, blank=True,null=True)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    user_code = models.CharField(max_length=8, unique=True, blank=True)
    date_joined = models.DateTimeField(default=timezone.now)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []  # Не нужно username при createsuperuser

    objects = UserManager()

    def __str__(self):
        return self.email


class EmailVerificationCode(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    code = models.CharField(max_length=6)
    created_at = models.DateTimeField(auto_now_add=True)
    is_used = models.BooleanField(default=False)

    def is_expired(self):
        return self.created_at + timedelta(minutes=10) < timezone.now()

    def __str__(self):
        return f"{self.user.email} – {self.code}"


# --- Игра ---
class Game(models.Model):
    title = models.CharField(max_length=255,unique=True)
    game_code = models.CharField(max_length=6,unique=True)

    subtitle = models.CharField(max_length=255, blank=True, null=True)  # Доп. подзаголовок
    description = models.TextField(help_text="Основное описание игры")

    how_to_play = models.TextField(blank=True, null=True, help_text="Как играть")
    target_audience = models.TextField(blank=True, null=True, help_text="Для кого игра")

    requires_device = models.BooleanField(default=False)

    price = models.DecimalField(max_digits=10, decimal_places=2)
    trial_available = models.BooleanField(default=True)

    cover_image = models.ImageField(upload_to='game_covers/', blank=True, null=True)

    # Новые поля:
    system_requirements = models.TextField(blank=True, null=True, help_text="Требования к системе")
    required_equipment = models.TextField(blank=True, null=True, help_text="Оборудование (микрофоны и т.д.)")

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.title

    def get_cover_image_url(self):
        """Безопасно возвращает URL обложки игры"""
        if self.cover_image:
            try:
                if hasattr(self.cover_image, 'path'):
                    import os
                    if os.path.exists(self.cover_image.path):
                        return self.cover_image.url
                else:
                    # Для облачного хранилища
                    return self.cover_image.url
            except (ValueError, OSError):
                pass
        return None



class UserGameAccess(models.Model):
    ACCESS_CHOICES = [
        ('oneday', 'One Day Access'),
        ('subscription', 'Subscription Access'),
        ('package', 'Package Access'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    game = models.ForeignKey(Game, on_delete=models.CASCADE)
    access_type = models.CharField(max_length=20, choices=ACCESS_CHOICES)
    access_start = models.DateTimeField(null=True, blank=True)
    access_end = models.DateTimeField(null=True, blank=True)
    activated = models.BooleanField(default=False)
    package_subscription = models.ForeignKey('UserPackageSubscription', on_delete=models.CASCADE, null=True, blank=True)

    def has_access(self):
        now = timezone.now()
        if not self.activated:
            return False
        return self.access_start <= now <= self.access_end



class GameGalleryItem(models.Model):
    game = models.ForeignKey(Game, on_delete=models.CASCADE, related_name='gallery_items')
    file = models.ImageField(upload_to='game_gallery/')
    uploaded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.game.title} – {self.file.name}"

    def get_file_url(self):
        """Безопасно возвращает URL файла галереи"""
        if self.file:
            try:
                if hasattr(self.file, 'path'):
                    import os
                    if os.path.exists(self.file.path):
                        return self.file.url
                else:
                    # Для облачного хранилища
                    return self.file.url
            except (ValueError, OSError):
                pass
        return None


# --- Файлы игры ---
def game_file_upload_path(instance, filename):
    """Генерирует путь для загрузки файлов игр"""
    return f'game_files/{instance.game.id}/{instance.platform}/{filename}'


class GameFile(models.Model):
    PLATFORM_CHOICES = [
        ('windows', 'Windows'),
        ('mac', 'macOS'),
        ('linux', 'Linux'),
        ('android', 'Android'),
        ('ios', 'iOS'),
        ('web', 'Web'),
    ]

    game = models.ForeignKey(Game, on_delete=models.CASCADE, related_name='game_files')
    file = models.FileField(upload_to=game_file_upload_path, help_text="Файл игры")
    platform = models.CharField(max_length=20, choices=PLATFORM_CHOICES, help_text="Платформа")
    version = models.CharField(max_length=50, blank=True, null=True, help_text="Версия игры")
    description = models.TextField(blank=True, null=True, help_text="Описание файла")
    is_active = models.BooleanField(default=True, help_text="Активен ли файл")
    uploaded_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('game', 'platform', 'version')  # Один файл на платформу и версию

    def __str__(self):
        return f"{self.game.title} - {self.platform} v{self.version or '1.0'}"

    @property
    def file_name(self):
        """Возвращает имя файла"""
        if self.file:
            return self.file.name.split('/')[-1]
        return None

    @property
    def file_size(self):
        """Возвращает размер файла в байтах"""
        if self.file:
            return self.file.size
        return None


class GamePackage(models.Model):
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    benefit_1= models.CharField(max_length=255,blank=True)
    benefit_2= models.CharField(max_length=255,blank=True)
    benefit_3= models.CharField(max_length=255,blank=True)
    games = models.ManyToManyField(Game, related_name='packages')
    duration_days = models.PositiveIntegerField(default=30)
    price = models.DecimalField(max_digits=8, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    max_selectable_games = models.PositiveIntegerField(default=2)

    def __str__(self):
        return self.name



# --- Ключи игры ---
class GameKey(models.Model):
    game = models.ForeignKey(Game, on_delete=models.CASCADE, related_name='keys')
    code = models.CharField(max_length=255, unique=True)
    is_used = models.BooleanField(default=False)
    assigned_to_user = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL)
    assigned_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.game.title} - {self.code}"



class GameSession(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    game_key = models.ForeignKey(GameKey, on_delete=models.CASCADE)
    started_at = models.DateTimeField(auto_now_add=True)
    ended_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.user.email} - {self.game_key}"



class UserLibrary(models.Model):
    ACCESS_SOURCE_CHOICES = [
        ('individual', 'Individual Purchase'),
        ('package', 'Package Subscription'),
    ]

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='library')
    game = models.ForeignKey(Game, on_delete=models.CASCADE, related_name='library_entries')
    access_source = models.CharField(max_length=20, choices=ACCESS_SOURCE_CHOICES, default='individual')
    package_subscription = models.ForeignKey('UserPackageSubscription', on_delete=models.CASCADE, null=True, blank=True)
    added_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'game')  # Один пользователь — одна игра максимум

    def __str__(self):
        return f"{self.user.email} — {self.game.title} ({self.access_source})"


# --- Подписки на пакеты ---
class UserPackageSubscription(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='package_subscriptions')
    package = models.ForeignKey(GamePackage, on_delete=models.CASCADE)
    purchase = models.OneToOneField('Purchase', on_delete=models.CASCADE)
    selected_games = models.ManyToManyField(Game, blank=True, help_text="Выбранные игры из пакета")
    games_selected_count = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    activated_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()

    class Meta:
        unique_together = ('user', 'package')  # Один пользователь может иметь только одну подписку на пакет

    def __str__(self):
        return f"{self.user.email} - {self.package.name}"

    def can_select_more_games(self):
        """Проверяет, может ли пользователь выбрать ещё игры"""
        return self.games_selected_count < self.package.max_selectable_games

    def remaining_game_slots(self):
        """Возвращает количество оставшихся слотов для выбора игр"""
        return self.package.max_selectable_games - self.games_selected_count

    def is_expired(self):
        """Проверяет, истекла ли подписка"""
        return timezone.now() > self.expires_at


# --- Покупка ---
class Purchase(models.Model):
    PURCHASE_TYPES = (
        ('trial', 'Пробный доступ'),
        ('game', 'Игра'),
        ('package', 'Пакет'),
    )
    STATUS_CHOICES = (
        ('pending', 'Ожидает оплаты'),
        ('paid', 'Оплачено'),
        ('cancelled', 'Отменено'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='purchases')
    game = models.ForeignKey(Game, on_delete=models.SET_NULL , null=True, blank=True)
    package = models.ForeignKey(GamePackage, on_delete=models.SET_NULL, null=True, blank=True)
    purchase_type = models.CharField(max_length=20, choices=PURCHASE_TYPES)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)


    def __str__(self):
        return f"{self.user.email} - {self.game.title} ({self.purchase_type})"


class PackageGameSelection(models.Model):
    purchase = models.OneToOneField(Purchase, on_delete=models.CASCADE)
    selected_games = models.ManyToManyField(Game)

    def clean(self):
        # Валидация: количество игр не больше допустимого
        max_games = self.purchase.package.max_selectable_games
        if self.selected_games.count() > max_games:
            raise ValidationError(f'Вы можете выбрать максимум {max_games} игр.')



class CartItem(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='cart_items')
    game = models.ForeignKey(Game, on_delete=models.CASCADE, null=True, blank=True)  # Временно nullable для миграции
    quantity = models.PositiveIntegerField(default=1)
    added_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'game')


# --- Платежи ---
class Payment(models.Model):
    STATUS_CHOICES = (
        ('pending', 'Ожидание'),
        ('success', 'Успешно'),
        ('failed', 'Ошибка'),
    )

    PROVIDER_CHOICES = (
        ('kaspi', 'Kaspi'),
        ('stripe', 'Stripe'),
        ('other', 'Другое'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    purchase = models.ForeignKey(Purchase, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    provider = models.CharField(max_length=50, choices=PROVIDER_CHOICES)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.email} - {self.provider} - {self.status}"



