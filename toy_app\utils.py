from django.core.mail import send_mail
from .models import EmailVerificationCode
import random

def send_email_verification_code(user):
    code = str(random.randint(100000, 999999))

    EmailVerificationCode.objects.create(
        user=user,
        code=code
    )

    send_mail(
        'Ваш код подтверждения',
        f'Код для подтверждения регистрации: {code}',
        '<EMAIL>',
        [user.email]
    )