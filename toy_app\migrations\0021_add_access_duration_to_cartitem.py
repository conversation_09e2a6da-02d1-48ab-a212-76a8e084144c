# Generated by Django 5.1.7 on 2025-07-24 14:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('toy_app', '0020_remove_cartitem_game_package_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='cartitem',
            name='access_duration',
            field=models.CharField(choices=[('day', 'One Day Access'), ('month', 'One Month Access')], default='day', help_text='Duration of access for this game', max_length=10),
        ),
    ]
