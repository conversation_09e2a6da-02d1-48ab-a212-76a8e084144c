# Access Duration API Documentation

## Overview
The system now supports choosing access duration when adding games to cart or purchasing packages. Users can select between:
- **Day Access**: 1-day access (immediate activation)
- **Month Access**: 30-day access (immediate activation)

## Pricing Logic
- **Day Access**: Base game price
- **Month Access**: Base game price × 2.5

## Cart API with Access Duration

### 1. Add Single Game to Cart
**Endpoint:** `POST /api/cart/`

**Request:**
```json
{
    "game": 1,
    "access_duration": "day",
    "quantity": 1
}
```

**Response:**
```json
{
    "id": 123,
    "user": 1,
    "game": 1,
    "game_obj": {
        "id": 1,
        "title": "Game Title",
        "price": "19.99",
        "cover_image_url": "http://localhost:8000/media/game_covers/game1.jpg"
    },
    "quantity": 1,
    "access_duration": "day",
    "calculated_price": 19.99,
    "added_at": "2025-07-24T19:30:00Z"
}
```

### 2. Add Multiple Games to Cart
**Endpoint:** `POST /api/cart/add-multiple/`

**Request:**
```json
{
    "games": [1, 2, 3],
    "access_duration": "month"
}
```

**Response:**
```json
{
    "message": "Successfully added 3 games to cart",
    "results": {
        "added": [
            {
                "game_id": 1,
                "game_title": "Game 1",
                "cart_item_id": 124,
                "access_duration": "month",
                "calculated_price": 49.98
            }
        ],
        "skipped": [],
        "errors": []
    },
    "summary": {
        "total_requested": 3,
        "added": 3,
        "skipped": 0,
        "errors": 0
    }
}
```

### 3. Update Access Duration for Cart Item
**Endpoint:** `PATCH /api/cart/{cart_item_id}/update-access-duration/`

**Request:**
```json
{
    "access_duration": "month"
}
```

**Response:**
```json
{
    "message": "Access duration updated successfully",
    "cart_item": {
        "id": 123,
        "access_duration": "month",
        "calculated_price": 49.98
    }
}
```

### 4. Cart Preview
**Endpoint:** `GET /api/cart/preview/`

**Response:**
```json
{
    "message": "Cart preview",
    "cart_items": [
        {
            "id": 123,
            "game_obj": {
                "id": 1,
                "title": "Game 1",
                "price": "19.99"
            },
            "access_duration": "day",
            "calculated_price": 19.99
        },
        {
            "id": 124,
            "game_obj": {
                "id": 2,
                "title": "Game 2",
                "price": "24.99"
            },
            "access_duration": "month",
            "calculated_price": 62.48
        }
    ],
    "summary": {
        "total_items": 2,
        "total_price": 82.47,
        "day_access_count": 1,
        "month_access_count": 1
    }
}
```

### 5. Purchase Cart (Checkout)
**Endpoint:** `POST /api/checkout/`

**Request:** No body required

**Response:**
```json
{
    "detail": "Покупки успешно обработаны.",
    "purchases": [
        {
            "id": 101,
            "game": {
                "id": 1,
                "title": "Game 1"
            },
            "price": "19.99",
            "status": "paid"
        }
    ],
    "skipped": []
}
```

## Package API with Access Duration

### 1. Package Preview with Access Duration
**Endpoint:** `POST /api/packages/preview-with-access/`

**Request:**
```json
{
    "package_id": 1,
    "selected_games_with_duration": [
        {
            "game_id": 1,
            "access_duration": "day"
        },
        {
            "game_id": 2,
            "access_duration": "month"
        }
    ]
}
```

**Response:**
```json
{
    "preview": {
        "package": {
            "id": 1,
            "name": "Premium Package",
            "price": 49.99,
            "duration_days": 30,
            "max_selectable_games": 3
        },
        "selected_games": [
            {
                "id": 1,
                "title": "Game 1",
                "access_duration": "day",
                "cover_image_url": "http://localhost:8000/media/game_covers/game1.jpg"
            },
            {
                "id": 2,
                "title": "Game 2",
                "access_duration": "month",
                "cover_image_url": "http://localhost:8000/media/game_covers/game2.jpg"
            }
        ],
        "total_price": 49.99,
        "games_count": 2,
        "expires_in_days": 30,
        "warnings": []
    },
    "can_purchase": true,
    "message": "Предварительный просмотр покупки пакета с выбором доступа"
}
```

### 2. Purchase Package with Access Duration
**Endpoint:** `POST /api/packages/purchase-with-access/`

**Request:**
```json
{
    "package_id": 1,
    "selected_games_with_duration": [
        {
            "game_id": 1,
            "access_duration": "day"
        },
        {
            "game_id": 2,
            "access_duration": "month"
        }
    ]
}
```

**Response:**
```json
{
    "detail": "Пакет успешно приобретён с выбранными играми и доступом.",
    "subscription_id": 123,
    "expires_at": "2025-08-23T19:30:00Z",
    "selected_games": [
        {
            "id": 1,
            "title": "Game 1",
            "access_duration": "day"
        },
        {
            "id": 2,
            "title": "Game 2",
            "access_duration": "month"
        }
    ],
    "games_count": 2,
    "max_selectable_games": 3
}
```

## Access Duration Logic

### Individual Game Purchases (Cart)
- **Day Access**: Creates `UserGameAccess` with `access_type='oneday'`, activated immediately for 1 day
- **Month Access**: Creates `UserGameAccess` with `access_type='subscription'`, activated immediately for 30 days

### Package Purchases
- **Package Duration**: User gets package subscription for the full package duration (e.g., 30 days)
- **Game Access Duration**: Each selected game gets immediate access based on chosen duration:
  - **Day**: 1-day immediate access
  - **Month**: 30-day immediate access (same as package duration)

### Pricing
- **Cart Items**: Price varies based on access duration (day = base price, month = base price × 2.5)
- **Package Items**: Package price is fixed regardless of individual game access duration choices

## Error Handling

### Common Validation Errors
```json
{
    "error": "access_duration must be \"day\" or \"month\""
}
```

```json
{
    "error": "У вас уже есть активный доступ к некоторым играм.",
    "already_owned_games": [
        {"id": 1, "title": "Game 1"}
    ]
}
```

```json
{
    "error": "У вас уже есть активная подписка на пакет.",
    "existing_subscription": {
        "package_name": "Premium Package",
        "expires_at": "2025-08-23T19:30:00Z"
    }
}
```

## Migration from Old System

### Backward Compatibility
- Old cart endpoints still work (default to 'day' access)
- Old package endpoints still work (no access duration choice)
- New endpoints provide enhanced functionality

### Recommended Migration Path
1. Update frontend to use new cart endpoints with access duration
2. Update package purchase flow to use new endpoints
3. Gradually phase out old endpoints

## Frontend Implementation Examples

### JavaScript/React Cart Implementation

```javascript
// Add game to cart with access duration choice
const addToCart = async (gameId, accessDuration = 'day') => {
    const response = await fetch('/api/cart/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
            game: gameId,
            access_duration: accessDuration,
            quantity: 1
        })
    });

    if (response.ok) {
        const data = await response.json();
        console.log(`Added game to cart with ${accessDuration} access for $${data.calculated_price}`);
        return data;
    } else {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to add to cart');
    }
};

// Update access duration for existing cart item
const updateCartItemDuration = async (cartItemId, newDuration) => {
    const response = await fetch(`/api/cart/${cartItemId}/update-access-duration/`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
            access_duration: newDuration
        })
    });

    if (response.ok) {
        const data = await response.json();
        console.log('Access duration updated:', data.cart_item);
        return data;
    }
};

// Get cart preview with totals
const getCartPreview = async () => {
    const response = await fetch('/api/cart/preview/', {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    });

    if (response.ok) {
        const data = await response.json();
        return data;
    }
};

// Checkout cart
const checkout = async () => {
    const response = await fetch('/api/checkout/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        }
    });

    if (response.ok) {
        const data = await response.json();
        console.log('Checkout successful:', data);
        return data;
    }
};
```

### Package Purchase with Access Duration

```javascript
// Preview package purchase with access duration choices
const previewPackageWithAccess = async (packageId, gamesWithDuration) => {
    const response = await fetch('/api/packages/preview-with-access/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
            package_id: packageId,
            selected_games_with_duration: gamesWithDuration
        })
    });

    const data = await response.json();

    if (data.can_purchase) {
        console.log('Package preview:', data.preview);
        return data;
    } else {
        console.warn('Cannot purchase:', data.preview.warnings);
        return data;
    }
};

// Purchase package with access duration choices
const purchasePackageWithAccess = async (packageId, gamesWithDuration) => {
    const response = await fetch('/api/packages/purchase-with-access/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
            package_id: packageId,
            selected_games_with_duration: gamesWithDuration
        })
    });

    if (response.ok) {
        const data = await response.json();
        console.log('Package purchased successfully:', data);
        return data;
    } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to purchase package');
    }
};

// Example usage
const exampleUsage = async () => {
    try {
        // Add games to cart with different access durations
        await addToCart(1, 'day');
        await addToCart(2, 'month');

        // Preview cart
        const cartPreview = await getCartPreview();
        console.log('Cart total:', cartPreview.summary.total_price);

        // Checkout
        await checkout();

        // Or purchase a package with access duration choices
        const packagePreview = await previewPackageWithAccess(1, [
            { game_id: 3, access_duration: 'day' },
            { game_id: 4, access_duration: 'month' }
        ]);

        if (packagePreview.can_purchase) {
            await purchasePackageWithAccess(1, [
                { game_id: 3, access_duration: 'day' },
                { game_id: 4, access_duration: 'month' }
            ]);
        }

    } catch (error) {
        console.error('Error:', error.message);
    }
};
```

### React Component Example

```jsx
import React, { useState } from 'react';

const GameCard = ({ game, onAddToCart }) => {
    const [accessDuration, setAccessDuration] = useState('day');
    const [isLoading, setIsLoading] = useState(false);

    const calculatePrice = (basePrice, duration) => {
        return duration === 'month' ? basePrice * 2.5 : basePrice;
    };

    const handleAddToCart = async () => {
        setIsLoading(true);
        try {
            await onAddToCart(game.id, accessDuration);
        } catch (error) {
            console.error('Failed to add to cart:', error);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="game-card">
            <h3>{game.title}</h3>
            <img src={game.cover_image_url} alt={game.title} />

            <div className="access-duration-selector">
                <label>
                    <input
                        type="radio"
                        value="day"
                        checked={accessDuration === 'day'}
                        onChange={(e) => setAccessDuration(e.target.value)}
                    />
                    1 Day - ${calculatePrice(game.price, 'day')}
                </label>

                <label>
                    <input
                        type="radio"
                        value="month"
                        checked={accessDuration === 'month'}
                        onChange={(e) => setAccessDuration(e.target.value)}
                    />
                    1 Month - ${calculatePrice(game.price, 'month')}
                </label>
            </div>

            <button
                onClick={handleAddToCart}
                disabled={isLoading}
                className="add-to-cart-btn"
            >
                {isLoading ? 'Adding...' : 'Add to Cart'}
            </button>
        </div>
    );
};

const CartPreview = ({ cartData, onUpdateDuration, onCheckout }) => {
    return (
        <div className="cart-preview">
            <h2>Cart Preview</h2>

            {cartData.cart_items.map(item => (
                <div key={item.id} className="cart-item">
                    <span>{item.game_obj.title}</span>

                    <select
                        value={item.access_duration}
                        onChange={(e) => onUpdateDuration(item.id, e.target.value)}
                    >
                        <option value="day">1 Day</option>
                        <option value="month">1 Month</option>
                    </select>

                    <span>${item.calculated_price}</span>
                </div>
            ))}

            <div className="cart-summary">
                <p>Total Items: {cartData.summary.total_items}</p>
                <p>Day Access: {cartData.summary.day_access_count}</p>
                <p>Month Access: {cartData.summary.month_access_count}</p>
                <p><strong>Total: ${cartData.summary.total_price}</strong></p>
            </div>

            <button onClick={onCheckout} className="checkout-btn">
                Checkout
            </button>
        </div>
    );
};
```
