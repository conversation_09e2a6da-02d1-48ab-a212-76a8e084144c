from django.core.management.base import BaseCommand
from django.core.files.storage import default_storage
from toy_app.models import Game, GameFile, GameGalleryItem
import os


class Command(BaseCommand):
    help = 'Очищает записи в базе данных, которые ссылаются на несуществующие файлы'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Показать что будет удалено, но не удалять',
        )
        parser.add_argument(
            '--delete-records',
            action='store_true',
            help='Удалить записи из базы данных (по умолчанию только очищает поля файлов)',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        delete_records = options['delete_records']
        
        self.stdout.write(self.style.SUCCESS('Начинаем проверку файлов...'))
        
        # Проверяем обложки игр
        self.check_game_covers(dry_run)
        
        # Проверяем файлы галереи
        self.check_gallery_files(dry_run, delete_records)
        
        # Проверяем файлы игр
        self.check_game_files(dry_run, delete_records)
        
        self.stdout.write(self.style.SUCCESS('Проверка завершена!'))

    def check_game_covers(self, dry_run):
        """Проверяет обложки игр"""
        self.stdout.write('Проверяем обложки игр...')
        
        games_with_covers = Game.objects.exclude(cover_image='').exclude(cover_image__isnull=True)
        missing_covers = 0
        
        for game in games_with_covers:
            try:
                if hasattr(game.cover_image, 'path'):
                    if not os.path.exists(game.cover_image.path):
                        missing_covers += 1
                        self.stdout.write(
                            self.style.WARNING(f'Отсутствует обложка: {game.title} - {game.cover_image.name}')
                        )
                        if not dry_run:
                            game.cover_image = None
                            game.save()
                            self.stdout.write(self.style.SUCCESS(f'Очищена обложка для игры: {game.title}'))
            except (ValueError, OSError):
                missing_covers += 1
                self.stdout.write(
                    self.style.WARNING(f'Ошибка доступа к обложке: {game.title} - {game.cover_image.name}')
                )
                if not dry_run:
                    game.cover_image = None
                    game.save()
                    self.stdout.write(self.style.SUCCESS(f'Очищена обложка для игры: {game.title}'))
        
        self.stdout.write(f'Найдено отсутствующих обложек: {missing_covers}')

    def check_gallery_files(self, dry_run, delete_records):
        """Проверяет файлы галереи"""
        self.stdout.write('Проверяем файлы галереи...')
        
        gallery_items = GameGalleryItem.objects.all()
        missing_files = 0
        
        for item in gallery_items:
            try:
                if hasattr(item.file, 'path'):
                    if not os.path.exists(item.file.path):
                        missing_files += 1
                        self.stdout.write(
                            self.style.WARNING(f'Отсутствует файл галереи: {item.game.title} - {item.file.name}')
                        )
                        if not dry_run:
                            if delete_records:
                                item.delete()
                                self.stdout.write(self.style.SUCCESS(f'Удалена запись галереи: {item.id}'))
                            else:
                                item.file = None
                                item.save()
                                self.stdout.write(self.style.SUCCESS(f'Очищен файл галереи: {item.id}'))
            except (ValueError, OSError):
                missing_files += 1
                self.stdout.write(
                    self.style.WARNING(f'Ошибка доступа к файлу галереи: {item.game.title} - {item.file.name}')
                )
                if not dry_run:
                    if delete_records:
                        item.delete()
                        self.stdout.write(self.style.SUCCESS(f'Удалена запись галереи: {item.id}'))
                    else:
                        item.file = None
                        item.save()
                        self.stdout.write(self.style.SUCCESS(f'Очищен файл галереи: {item.id}'))
        
        self.stdout.write(f'Найдено отсутствующих файлов галереи: {missing_files}')

    def check_game_files(self, dry_run, delete_records):
        """Проверяет файлы игр"""
        self.stdout.write('Проверяем файлы игр...')
        
        game_files = GameFile.objects.all()
        missing_files = 0
        
        for game_file in game_files:
            try:
                if hasattr(game_file.file, 'path'):
                    if not os.path.exists(game_file.file.path):
                        missing_files += 1
                        self.stdout.write(
                            self.style.WARNING(f'Отсутствует файл игры: {game_file.game.title} - {game_file.file.name}')
                        )
                        if not dry_run:
                            if delete_records:
                                game_file.delete()
                                self.stdout.write(self.style.SUCCESS(f'Удалена запись файла игры: {game_file.id}'))
                            else:
                                game_file.file = None
                                game_file.save()
                                self.stdout.write(self.style.SUCCESS(f'Очищен файл игры: {game_file.id}'))
            except (ValueError, OSError):
                missing_files += 1
                self.stdout.write(
                    self.style.WARNING(f'Ошибка доступа к файлу игры: {game_file.game.title} - {game_file.file.name}')
                )
                if not dry_run:
                    if delete_records:
                        game_file.delete()
                        self.stdout.write(self.style.SUCCESS(f'Удалена запись файла игры: {game_file.id}'))
                    else:
                        game_file.file = None
                        game_file.save()
                        self.stdout.write(self.style.SUCCESS(f'Очищен файл игры: {game_file.id}'))
        
        self.stdout.write(f'Найдено отсутствующих файлов игр: {missing_files}')
